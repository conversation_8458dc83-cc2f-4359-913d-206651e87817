import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:gap/gap.dart';
import 'package:gotcha_mfg_app/config/router/app_router.gr.dart';
import 'package:gotcha_mfg_app/config/theme/app_colors.dart';
import 'package:gotcha_mfg_app/core/extensions/app_text_theme_extension.dart';
import 'package:gotcha_mfg_app/core/utils/snackbar_service.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_detail_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/models/emotions_response.dart';
import 'package:gotcha_mfg_app/features/home/<USER>/widgets/mood_row.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboard/onboarding_cubit.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/blocs/onboarding_data/onboarding_data_cubit.dart';
import 'package:gotcha_mfg_app/features/onboarding/presentation/widgets/onboarding_sub_emotion_section.dart';
import 'package:gotcha_mfg_app/shared/widgets/app_header.dart';
import 'package:gotcha_mfg_app/shared/widgets/loading_widget.dart';
import 'package:gotcha_mfg_app/shared/widgets/primary_button.dart';
import 'package:gotcha_mfg_app/shared/widgets/retry_widget.dart';
import 'package:gotcha_mfg_app/core/extensions/mood_list_extensions.dart';

import '../../../../core/mixpanel_service.dart';
import '../../../../core/utils/platform_utils.dart';
import '../../../../locator.dart';

@RoutePage()
class OnboardingFeelingPage extends StatefulWidget {
  const OnboardingFeelingPage({super.key});

  @override
  State<OnboardingFeelingPage> createState() => _OnboardingFeelingPageState();
}

class _OnboardingFeelingPageState extends State<OnboardingFeelingPage> {
  int selectedItem = -1;

  EmotionsResponse? emotionsResponse;
  List<Type>? primaryEmotions;
  EmotionsDetailResponse? emotionsDetailResponse;

  @override
  void initState() {
    super.initState();
    sl<MixpanelService>().trackScreenView(
      'Onboarding Feeling Page',
      properties: {'Code': 'screen_view.onboarding_feeling_page'},
    );

    context.read<OnboardingCubit>().getPrimaryEmotions();
  }

  void _updateSelectedItem(int index) {
    setState(() {
      selectedItem = (selectedItem == index) ? -1 : index;
    });

    if (selectedItem >= 0) {
      context
          .read<OnboardingCubit>()
          .getDetailEmotions(primaryEmotions?[selectedItem].id ?? '');
    }
    context.read<OnboardingDataCubit>().clear();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final size = MediaQuery.sizeOf(context);
    final isKeyboardOpen = MediaQuery.of(context).viewInsets.bottom != 0;

    return BlocConsumer<OnboardingCubit, OnboardingState>(
      listener: (context, state) {
        if (state is OnboardingLoaded) {
          emotionsResponse = state.onboardingResponse;
          primaryEmotions = emotionsResponse?.data?.type?.sortByPoint();
          emotionsDetailResponse = state.detailResponse;
        }
        if (state is OnboardingError) {
          SnackBarService.error(
            context: context,
            message: state.error,
          );
        }
      },
      builder: (context, state) {
        if (state is OnboardingLoading) {
          return const LoadingWidget(color: Colors.white);
        }
        if (state is OnboardingError) {
          return RetryWidget(
            onRetry: () {
              context.read<OnboardingCubit>().getPrimaryEmotions();
            },
            color: Colors.white,
          );
        }
        return Scaffold(
          appBar: AppBar(
            toolbarHeight: 0,
            elevation: 0,
            systemOverlayStyle: const SystemUiOverlayStyle(
              statusBarColor: Colors.white,
              systemNavigationBarIconBrightness: Brightness.dark,
              statusBarBrightness: Brightness.light,
              statusBarIconBrightness: Brightness.dark,
              systemNavigationBarColor: AppColors.grey,
            ),
          ),
          resizeToAvoidBottomInset: true,
          body: GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            behavior: HitTestBehavior.translucent,
            child: Padding(
              padding: EdgeInsets.only(
                top: isIos ? 4 : 8,
                left: 8,
                right: 8,
              ),
              child: Column(
                children: [
                  const AppHeader(
                    title: 'Getting started',
                    // title: '',
                    currentStep: 2,
                    totalSteps: 4,
                  ),
                  Expanded(
                    child: Container(
                      color: AppColors.navy,
                      child: Container(
                        padding: EdgeInsets.only(bottom: isIos ? 80 : 56),
                        decoration: const BoxDecoration(
                          borderRadius: BorderRadius.only(
                            topLeft: Radius.circular(30),
                            topRight: Radius.circular(30),
                          ),
                          color: AppColors.grey,
                        ),
                        child: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              const Gap(12),
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 24,
                                  vertical: 16,
                                ),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      emotionsResponse?.data?.question?[0]
                                              .description ??
                                          'N/A',
                                      style: textTheme.ralewayRegular
                                          .copyWith(fontSize: 14),
                                    ),
                                    const Gap(4),
                                    Text(
                                      emotionsResponse
                                              ?.data?.question?[0].question ??
                                          'N/A',
                                      style: textTheme.ralewaySemiBold.copyWith(
                                        fontSize: 17,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              const Gap(4),
                              Padding(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 24),
                                child: MoodRow(
                                  moods: primaryEmotions ?? [],
                                  selectedItem: selectedItem,
                                  onSelectedItemChanged: _updateSelectedItem,
                                ),
                              ),
                              if (selectedItem >= 0) ...[
                                if (state is OnboardingDetailLoading)
                                  SizedBox(
                                    height: size.height * 0.5,
                                    child: const LoadingWidget(
                                        color: AppColors.grey),
                                  )
                                else if (emotionsDetailResponse
                                        ?.data?.isNotEmpty ??
                                    false)
                                  ...emotionsDetailResponse!.data!
                                      .where((section) =>
                                          section.orderOfQuestion == 1)
                                      .map((section) {
                                    return Padding(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 24.0),
                                      child: OnboardingSubEmotionSection(
                                        optional: section.isOptional ?? false,
                                        title: '',
                                        items: section.answers ?? [],
                                        checkInTypeId:
                                            primaryEmotions?[selectedItem].id,
                                        questionId: section.questionId,
                                      ),
                                    );
                                  }),
                              ],
                              const Gap(120),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          floatingActionButtonLocation:
              FloatingActionButtonLocation.centerDocked,
          floatingActionButton: isKeyboardOpen
              ? const SizedBox()
              : ((selectedItem >= 0) && (state is OnboardingLoaded))
                  ? Padding(
                      padding: const EdgeInsets.symmetric(
                          horizontal: 32, vertical: 24),
                      child: SizedBox(
                        width: size.width,
                        child: PrimaryButton(
                          text: 'Next',
                          isEnabled: true,
                          onPressed: () {
                            sl<MixpanelService>()
                                .trackButtonClick('Next', properties: {
                              'Page': 'Onboarding Feeling Page',
                              'Feeling': primaryEmotions?[selectedItem].name,
                              'Code': 'click.onboarding_feeling_page.next'
                            });

                            context.pushRoute(
                              OnBoardingThanksRoute(
                                data: emotionsDetailResponse,
                                description:
                                    primaryEmotions?[selectedItem].description,
                                checkInTypeId:
                                    primaryEmotions?[selectedItem].id ?? '',
                              ),
                            );
                          },
                        ),
                      ),
                    )
                  : const SizedBox(),
        );
      },
    );
  }
}
